{"version": "0.2.0", "configurations": [{"name": "Electron: Main", "type": "node", "request": "launch", "protocol": "inspector", "runtimeExecutable": "npm", "runtimeArgs": ["run", "start"], "env": {"MAIN_ARGS": "--inspect=5858 --remote-debugging-port=9223"}}, {"name": "Electron: Renderer", "type": "chrome", "request": "attach", "port": 9223, "webRoot": "${workspaceFolder}", "timeout": 15000}], "compounds": [{"name": "Electron: All", "configurations": ["Electron: Main", "Electron: Renderer"]}]}